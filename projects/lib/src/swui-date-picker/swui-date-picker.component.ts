import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';
import { FormControl, FormGroupDirective, NgControl } from '@angular/forms';
import { FocusMonitor } from '@angular/cdk/a11y';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import * as moment from 'moment';
import 'moment-timezone';
import { SwuiDatePickerConfig, SwuiDatePickerConfigModel } from './swui-date-picker-config.model';
import { MatMenuTrigger } from '@angular/material/menu';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';


const CONTROL_NAME = 'lib-swui-date-picker';
let nextUniqueId = 0;

export function processInputString( val: string, config: SwuiDatePickerConfigModel ): string {
  const { dateFormat, timePicker, timeFormat, timeZone } = config;
  const format = timePicker ? dateFormat + ' ' + timeFormat : dateFormat;
  let processed = '';
  if (moment.utc(val).isValid()) {
    if (timeZone) {
      processed = moment.tz(val, timeZone).format(format);
    } else {
      processed = moment.utc(val).format(format);
    }
  }
  return processed;
}

@Component({
  selector: 'lib-swui-date-picker',
  templateUrl: './swui-date-picker.component.html',
  styleUrls: ['./swui-date-picker.component.scss'],
  providers: [{ provide: MatFormFieldControl, useExisting: SwuiDatePickerComponent }],
})

export class SwuiDatePickerComponent extends SwuiMatFormFieldControl<string> implements OnInit {
  @Input() minDate = '';
  @Input() maxDate = '';

  @Input()
  set config( val: SwuiDatePickerConfig ) {
    this._config$.next(new SwuiDatePickerConfigModel(val));
  }

  get config(): SwuiDatePickerConfig {
    return this._config$.value;
  }

  @Input() title = '';

  @Input()
  set value( val: string ) {
    this._value$.next(val);
  }

  get value(): string {
    return this._value$.value;
  }

  get empty() {
    return !this.value;
  }

  readonly inputControl = new FormControl();
  readonly dateControl = new FormControl();
  readonly controlType = CONTROL_NAME;

  @ViewChild(MatInput) input?: MatInput;
  @ViewChild(MatMenuTrigger, { static: true }) menuTrigger?: MatMenuTrigger;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.inputControl.value;
  }

  private readonly _value$ = new BehaviorSubject<string>('');
  private readonly _config$ = new BehaviorSubject<SwuiDatePickerConfig>(new SwuiDatePickerConfigModel(undefined));
  private _sourceValue = '';

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
  }

  ngOnInit(): void {
    combineLatest([this._value$, this._config$])
      .pipe(
        map(( [val, config] ) => {
          const processed = val && moment.utc(val).isValid() ? val : '';
          return { processed, config };
        }),
        takeUntil(this.destroyed$)
      )
      .subscribe(val => {
        const { processed, config } = val;
        this._sourceValue = processed;
        this.dateControl.setValue(processed, { emitEvent: false });
        this.inputControl.setValue(processInputString(processed, config), { emitEvent: false });
        this.onChange(processed);
      });
  }

  onContainerClick( event: Event ): void {
    event.stopPropagation();
    if (this.elRef && this.menuTrigger && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.elRef.nativeElement.focus();
      this.menuTrigger.openMenu();
    }
  }

  writeValue( val: string ): void {
    this._value$.next(val);
  }

  cancel( event: Event ) {
    this.prevent(event);
    this._value$.next(this._sourceValue);
    if (this.menuTrigger) {
      this.menuTrigger.closeMenu();
    }
  }

  apply( event: Event ) {
    this.prevent(event);
    this._value$.next(this.dateControl.value);
    if (this.menuTrigger) {
      this.menuTrigger.closeMenu();
    }
  }

  clear( event: Event ) {
    this.prevent(event);
    this.dateControl.setValue('');
  }

  prevent( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
  }

  onOpen() {
    this.dateControl.setValue(this.dateControl.value);
  }

  protected onDisabledState( disabled: boolean ) {
    disabled ? this.inputControl.disable() : this.inputControl.enable();
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }
}
