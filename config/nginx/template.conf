# Increase server names hash bucket size to handle longer server names
server_names_hash_bucket_size 128;

upstream upstream_api {
  server ${MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_softgate_api {
  server ${SOFTGATE_MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

# Default/Fallback server block
server {
  listen 80 default_server;
  server_name _;

  # Return 444 (connection closed without response) for undefined hosts
  return 444;
}

# Main server block for regular hosts
server {
  listen 80;
  server_name localhost ~^(?!${SOFTGATE_HOST}$).*$;

  include /usr/share/nginx/common.conf;

  location /favicon.ico {
    try_files /favicon.ico =404;
    access_log off;
    expires 1y;
  }

  location /api/config {
    default_type application/json;
    return 200 '{
      "host": "$host",
      "bridge": "${BRIDGE_URL}",
      "loginUrl": "${LOGIN_URL}",
      "hubs": {
        "casino": "${CASINO_HUB_URL}",
        "engagement": "${ENGAGEMENT_HUB_URL}",
        "analytics": "${DATA_HUB_URL}",
        "studio": "${STUDIO_HUB_URL}"
      },
      "envName": "${ENV_NAME}",
      "locationName": "${LOCATION_NAME}"
    }';
  }

  location /v1 {
      proxy_pass http://upstream_api/v1;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Forwarded-Server $host;
  }
}

# Softgate virtual host
server {
  listen 80;
  server_name ${SOFTGATE_HOST};

  include /usr/share/nginx/common.conf;

  location /favicon.ico {
    try_files /img/softgate/favicon.ico /favicon.ico =404;
    access_log off;
    expires 1y;
  }

  location /api/config {
    default_type application/json;
    return 200 '{
      "host": "$host",
      "bridge": "${SOFTGATE_BRIDGE_URL}",
      "loginUrl": "${SOFTGATE_LOGIN_URL}",
      "hubs": {
        "casino": "${SOFTGATE_CASINO_HUB_URL}",
        "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",
        "analytics": "${SOFTGATE_DATA_HUB_URL}",
        "studio": "${SOFTGATE_STUDIO_HUB_URL}"
      },
      "logo": {
        "main": "/img/softgate/logo.png",
        "solo": "/img/softgate/logo.png",
        "white": ""
      },
      "envName": "${ENV_NAME}",
      "locationName": "${LOCATION_NAME}"
    }';
  }

  location /v1 {
      proxy_pass http://upstream_softgate_api/v1;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Forwarded-Server $host;
  }
}
