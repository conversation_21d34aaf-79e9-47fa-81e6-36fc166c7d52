import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { EntityCountriesComponent, EntityCountry } from './entity-countries.component';
import { filter, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { EntitySettingsModel } from 'src/app/common/models/entity-settings.model';
import { Country, getInheritedCountryCode, InheritedCountryCode } from 'src/app/common/typings';
import { CountryItem } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';
import { RowAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { CountryService } from 'src/app/common/services/country.service';
import { EntitySettingsService } from 'src/app/common/services/entity-settings.service';
import { Entity } from '../../../../../common/models/entity.model';

@Component({
  selector: '[entity-restricted-countries]',
  templateUrl: './entity-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityRestrictedCountriesComponent extends EntityCountriesComponent {
  title = 'ENTITY_SETUP.REGIONAL.titleRestrictedCountries';

  @Output() restrictedOwnList: EventEmitter<InheritedCountryCode[]> = new EventEmitter();

  @Input()
  set restrictedCountries(val: InheritedCountryCode[]) {
    this._restrictedCountries = val || [];
    this.restrictedOwnList.emit(val);
    this.refreshData();
  }

  get restrictedCountries(): InheritedCountryCode[] {
    return this._restrictedCountries;
  }
  private _restrictedCountries: InheritedCountryCode[] = [];

  private allAvailableCountries: string[] = [];

  constructor(
    private countryService: CountryService<Country>,
    dialog: MatDialog,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
    cdr: ChangeDetectorRef,
  ) {
    super(dialog, cdr);
  }

  ngOnInit() {
    this.countryService.getList().pipe(
      map((countries) => countries.map(country => country.code)),
      take(1)
    ).subscribe((countries) => {
      this.allAvailableCountries = countries;
    });
  }

  showCountryModal($event: Event): void {
    super.showCountryModal($event);
    const source = this.buildCountriesModal('restricted');
    source.pipe(
      filter((data: { entity: Entity, selected: string[] }) => !!data && typeof data.entity !== 'undefined'),
      switchMap(({ entity, selected }) => {
        selected = selected.length ? selected : null;
        return this.entitySettingsService.patchSettings({ restrictedCountries: selected }, entity.path);
      }),
      tap((settings: EntitySettingsModel) => this._entitySettings = settings),
      switchMap(() => this.entitySettingsService.getSettings(this.entity.path, true)),
      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountriesAdded'))),
      finalize(() => this.refreshData()),
      take(1)
    ).subscribe((ownEntitySettings: EntitySettingsModel) => {
      this.restrictedCountries = getInheritedCountryCode(ownEntitySettings.restrictedCountries, this._entitySettings.restrictedCountries);
    });
  }

  removeCountryModal(countryCode: string) {
    const removeDialog = this.buildRemoveCountryModal(countryCode);
    removeDialog.pipe(
      filter(code => !!code && code in this.countriesHash),
      switchMap((code: string) => {
        let removed = this.restrictedCountries.filter(country => country.code !== code);
        removed = removed.length ? removed : null;
        return this.entitySettingsService.patchSettings({ restrictedCountries: removed }, this.entity.path);
      }),
      tap((settings: EntitySettingsModel) => this._entitySettings = settings),
      switchMap(() => this.entitySettingsService.getSettings(this.entity.path, true)),
      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountryRemoved'))),
      take(1),
      finalize(() => this.refreshData())
    ).subscribe((ownEntitySettings: EntitySettingsModel) => {
      this.restrictedCountries = getInheritedCountryCode(ownEntitySettings.restrictedCountries, this._entitySettings.restrictedCountries);
    });
  }

  protected setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.actionDelete',
        icon: 'delete',
        inMenu: true,
        fn: ({ code }) => this.removeCountryModal(code),
        canActivateFn: () => !this.disabledList
      })
    ];
  }

  protected loadData(): EntityCountry[] {
    return this.restrictedCountries.map<EntityCountry>(({ code, inherited }) => ({
      code,
      displayName: this.countriesHash[code]?.displayName ?? '',
      isDefault: false,
      isInherited: inherited
    }));
  }

  protected buildAvailableCountries(): CountryItem[] {
    let countryList = this.allAvailableCountries;
    const rootParent = this.entity.entityParent.isRoot();
    const rootCountriesMismatch = countryList.length !== this.countries.length;

    if (rootParent && rootCountriesMismatch) {
      countryList = Object.keys(this.countriesHash);
    }

    return countryList.map((code) => {
      const item = new CountryItem(this.countriesHash[code]);
      item.selected = this.restrictedCountries.findIndex(country => country.code === code) > -1;
      if (code === this.entity.defaultCountry) {
        item.disabled = true;
      }
      return item;
    });
  }
}
